import React, { memo, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import * as Animatable from 'react-native-animatable';
import LinearGradient from 'react-native-linear-gradient';

import { theme, commonStyles } from '../theme/theme';
import { formatTime } from '../utils/formatUtils';
import AnimatedCard from './common/AnimatedCard';
import GlassCard from './common/GlassCard';

/**
 * MealCard Component - Displays meal information in a card format
 */
const MealCard = memo(({
  meal,
  onPress,
  onEdit,
  onDelete,
  showImage = true,
  showNutrition = true,
  compact = false,
}) => {
  const mealTypeData = useMemo(() => {
    const getMealTypeData = (type) => {
      switch (type) {
        case 'breakfast':
          return {
            color: '#FF9800',
            gradient: ['#FF9800', '#FFB74D'],
            icon: '🌅',
            bgIcon: '☀️',
          };
        case 'lunch':
          return {
            color: '#4CAF50',
            gradient: ['#4CAF50', '#81C784'],
            icon: '☀️',
            bgIcon: '🌞',
          };
        case 'dinner':
          return {
            color: '#2196F3',
            gradient: ['#2196F3', '#64B5F6'],
            icon: '🌙',
            bgIcon: '🌃',
          };
        case 'snack':
          return {
            color: '#9C27B0',
            gradient: ['#9C27B0', '#BA68C8'],
            icon: '🍎',
            bgIcon: '🥨',
          };
        default:
          return {
            color: theme.colors.onSurfaceVariant,
            gradient: [theme.colors.surfaceVariant, theme.colors.surface],
            icon: '🍽️',
            bgIcon: '🍴',
          };
      }
    };

    return getMealTypeData(meal.type);
  }, [meal.type]);

  const renderNutritionSummary = () => {
    if (!showNutrition) return null;

    const { totalNutrition } = meal;
    
    return (
      <View style={styles.nutritionContainer}>
        <View style={styles.nutritionItem}>
          <Text style={styles.nutritionValue}>{totalNutrition.calories}</Text>
          <Text style={styles.nutritionLabel}>Cal</Text>
        </View>
        <View style={styles.nutritionItem}>
          <Text style={styles.nutritionValue}>{totalNutrition.protein}g</Text>
          <Text style={styles.nutritionLabel}>Protein</Text>
        </View>
        <View style={styles.nutritionItem}>
          <Text style={styles.nutritionValue}>{totalNutrition.carbs}g</Text>
          <Text style={styles.nutritionLabel}>Carbs</Text>
        </View>
        <View style={styles.nutritionItem}>
          <Text style={styles.nutritionValue}>{totalNutrition.fat}g</Text>
          <Text style={styles.nutritionLabel}>Fat</Text>
        </View>
      </View>
    );
  };

  const renderFoodItems = () => {
    if (compact || meal.foodItems.length === 0) return null;

    const displayItems = meal.foodItems.slice(0, 3);
    const remainingCount = meal.foodItems.length - 3;

    return (
      <View style={styles.foodItemsContainer}>
        {displayItems.map((item, index) => (
          <Text key={item.id || index} style={styles.foodItemText}>
            • {item.name} ({item.portion})
          </Text>
        ))}
        {remainingCount > 0 && (
          <Text style={styles.moreItemsText}>
            +{remainingCount} more item{remainingCount > 1 ? 's' : ''}
          </Text>
        )}
      </View>
    );
  };

  const renderConfidenceBadge = () => {
    if (!meal.isAnalyzed || meal.analysisConfidence >= 0.8) return null;

    return (
      <View style={[styles.confidenceBadge, { backgroundColor: theme.colors.warning }]}>
        <Text style={styles.confidenceBadgeText}>
          {Math.round(meal.analysisConfidence * 100)}% confidence
        </Text>
      </View>
    );
  };

  const handlePress = useCallback(() => {
    onPress && onPress();
  }, [onPress]);

  return (
    <AnimatedCard
      style={[
        styles.container,
        compact && styles.compactCard,
      ]}
      onPress={handlePress}
      animationType="scale"
      elevation="md"
      borderRadius={theme.borderRadius.xl}
      backgroundColor="transparent"
    >
      <LinearGradient
        colors={['rgba(255,255,255,0.9)', 'rgba(255,255,255,0.7)']}
        style={styles.cardGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.header}>
          <View style={styles.mealInfo}>
            <View style={styles.mealTypeContainer}>
              <LinearGradient
                colors={mealTypeData.gradient}
                style={styles.mealTypeIconContainer}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <Text style={styles.mealTypeIcon}>
                  {mealTypeData.icon}
                </Text>
              </LinearGradient>
              <Text style={styles.mealTypeBgIcon}>
                {mealTypeData.bgIcon}
              </Text>
            </View>
            <View style={styles.mealDetails}>
              <Text style={styles.mealName} numberOfLines={1}>
                {meal.name || 'Unnamed Meal'}
              </Text>
              <Text style={styles.mealTime}>
                {formatTime(meal.timestamp)} • {meal.type}
              </Text>
            </View>
          </View>

          {meal.imageUri && showImage && (
            <View style={styles.imageContainer}>
              <Image source={{ uri: meal.imageUri }} style={styles.mealImage} />
              <LinearGradient
                colors={['transparent', 'rgba(0,0,0,0.3)']}
                style={styles.imageOverlay}
              />
            </View>
          )}
        </View>

        {renderFoodItems()}
        {renderNutritionSummary()}
        {renderConfidenceBadge()}

        {meal.isManuallyEdited && (
          <Animatable.View
            animation="pulse"
            iterationCount="infinite"
            style={styles.editedBadge}
          >
            <Text style={styles.editedBadgeText}>✏️ Edited</Text>
          </Animatable.View>
        )}

        {meal.notes && (
          <Text style={styles.notesText} numberOfLines={2}>
            {meal.notes}
          </Text>
        )}
      </LinearGradient>
    </AnimatedCard>
  );
});

const styles = StyleSheet.create({
  container: {
    marginVertical: theme.spacing.sm,
    marginHorizontal: theme.spacing.md,
    overflow: 'hidden',
  },

  cardGradient: {
    padding: theme.spacing.lg,
    borderRadius: theme.borderRadius.xl,
  },

  compactCard: {
    paddingVertical: theme.spacing.sm,
  },
  
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.sm,
  },
  
  mealInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  mealTypeContainer: {
    alignItems: 'center',
    marginRight: theme.spacing.sm,
  },
  
  mealTypeIcon: {
    fontSize: 24,
    marginBottom: theme.spacing.xs,
  },
  
  mealTypeDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  
  mealDetails: {
    flex: 1,
  },
  
  mealName: {
    ...theme.typography.h6,
    color: theme.colors.onSurface,
    marginBottom: 2,
  },
  
  mealTime: {
    ...theme.typography.caption,
    color: theme.colors.gray600,
    textTransform: 'capitalize',
  },
  
  mealImage: {
    width: 60,
    height: 60,
    borderRadius: theme.borderRadius.md,
    marginLeft: theme.spacing.sm,
  },
  
  foodItemsContainer: {
    marginBottom: theme.spacing.sm,
  },
  
  foodItemText: {
    ...theme.typography.body2,
    color: theme.colors.gray700,
    marginBottom: 2,
  },
  
  moreItemsText: {
    ...theme.typography.caption,
    color: theme.colors.gray500,
    fontStyle: 'italic',
    marginTop: 2,
  },
  
  nutritionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: theme.colors.gray50,
    borderRadius: theme.borderRadius.md,
    paddingVertical: theme.spacing.sm,
    marginTop: theme.spacing.sm,
  },
  
  nutritionItem: {
    alignItems: 'center',
  },
  
  nutritionValue: {
    ...theme.typography.h6,
    color: theme.colors.onSurface,
    marginBottom: 2,
  },
  
  nutritionLabel: {
    ...theme.typography.caption,
    color: theme.colors.gray600,
  },
  
  confidenceBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.sm,
    marginTop: theme.spacing.sm,
  },
  
  confidenceBadgeText: {
    ...theme.typography.caption,
    color: theme.colors.onPrimary,
    fontWeight: '600',
  },
  
  editedBadge: {
    position: 'absolute',
    top: theme.spacing.sm,
    right: theme.spacing.sm,
    backgroundColor: theme.colors.info,
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.sm,
  },
  
  editedBadgeText: {
    ...theme.typography.caption,
    color: theme.colors.onPrimary,
    fontSize: 10,
  },
  
  notesText: {
    ...theme.typography.body2,
    color: theme.colors.gray600,
    fontStyle: 'italic',
    marginTop: theme.spacing.sm,
    paddingTop: theme.spacing.sm,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray200,
  },
});

export default MealCard;

// Add display name for debugging
MealCard.displayName = 'MealCard';
