import React, { createContext, useContext, useState, useEffect } from 'react';
import { Appearance } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { lightTheme, darkTheme, getTheme } from '../theme/theme';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isSystemTheme, setIsSystemTheme] = useState(true);
  const [currentTheme, setCurrentTheme] = useState(lightTheme);

  // Load theme preference from storage
  useEffect(() => {
    loadThemePreference();
  }, []);

  // Listen to system theme changes
  useEffect(() => {
    if (isSystemTheme) {
      const subscription = Appearance.addChangeListener(({ colorScheme }) => {
        const systemIsDark = colorScheme === 'dark';
        setIsDarkMode(systemIsDark);
        setCurrentTheme(getTheme(systemIsDark ? 'dark' : 'light'));
      });

      // Set initial system theme
      const systemColorScheme = Appearance.getColorScheme();
      const systemIsDark = systemColorScheme === 'dark';
      setIsDarkMode(systemIsDark);
      setCurrentTheme(getTheme(systemIsDark ? 'dark' : 'light'));

      return () => subscription?.remove();
    }
  }, [isSystemTheme]);

  // Update theme when isDarkMode changes (manual theme)
  useEffect(() => {
    if (!isSystemTheme) {
      setCurrentTheme(getTheme(isDarkMode ? 'dark' : 'light'));
    }
  }, [isDarkMode, isSystemTheme]);

  const loadThemePreference = async () => {
    try {
      const savedThemeMode = await AsyncStorage.getItem('@theme_mode');
      const savedIsSystemTheme = await AsyncStorage.getItem('@is_system_theme');
      
      if (savedIsSystemTheme !== null) {
        const useSystemTheme = JSON.parse(savedIsSystemTheme);
        setIsSystemTheme(useSystemTheme);
        
        if (!useSystemTheme && savedThemeMode !== null) {
          const isDark = savedThemeMode === 'dark';
          setIsDarkMode(isDark);
          setCurrentTheme(getTheme(isDark ? 'dark' : 'light'));
        }
      }
    } catch (error) {
      console.error('Error loading theme preference:', error);
    }
  };

  const saveThemePreference = async (themeMode, useSystemTheme) => {
    try {
      await AsyncStorage.setItem('@theme_mode', themeMode);
      await AsyncStorage.setItem('@is_system_theme', JSON.stringify(useSystemTheme));
    } catch (error) {
      console.error('Error saving theme preference:', error);
    }
  };

  const toggleTheme = () => {
    const newIsDarkMode = !isDarkMode;
    setIsDarkMode(newIsDarkMode);
    setIsSystemTheme(false);
    saveThemePreference(newIsDarkMode ? 'dark' : 'light', false);
  };

  const setThemeMode = (mode) => {
    if (mode === 'system') {
      setIsSystemTheme(true);
      const systemColorScheme = Appearance.getColorScheme();
      const systemIsDark = systemColorScheme === 'dark';
      setIsDarkMode(systemIsDark);
      setCurrentTheme(getTheme(systemIsDark ? 'dark' : 'light'));
      saveThemePreference(systemIsDark ? 'dark' : 'light', true);
    } else {
      const isDark = mode === 'dark';
      setIsDarkMode(isDark);
      setIsSystemTheme(false);
      setCurrentTheme(getTheme(isDark ? 'dark' : 'light'));
      saveThemePreference(mode, false);
    }
  };

  const value = {
    theme: currentTheme,
    isDarkMode,
    isSystemTheme,
    toggleTheme,
    setThemeMode,
    themeMode: isSystemTheme ? 'system' : (isDarkMode ? 'dark' : 'light'),
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
