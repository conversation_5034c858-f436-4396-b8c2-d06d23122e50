import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { theme } from '../../theme/theme';

/**
 * Modern Animated Tab Bar Icon Component
 */
const TabBarIcon = ({ 
  routeName, 
  focused, 
  color, 
  size = 24 
}) => {
  const scale = useSharedValue(focused ? 1.2 : 1);
  const opacity = useSharedValue(focused ? 1 : 0.6);

  React.useEffect(() => {
    scale.value = withSpring(focused ? 1.2 : 1, theme.animations.spring.gentle);
    opacity.value = withTiming(focused ? 1 : 0.6, { 
      duration: theme.animations.duration.short 
    });
  }, [focused]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const getIconData = (routeName) => {
    switch (routeName) {
      case 'Home':
        return {
          icon: focused ? '🏠' : '🏡',
          gradient: focused,
          bgColor: theme.colors.primary,
        };
      case 'Camera':
        return {
          icon: '📷',
          gradient: focused,
          bgColor: theme.colors.secondary,
        };
      case 'History':
        return {
          icon: focused ? '📊' : '📈',
          gradient: focused,
          bgColor: theme.colors.tertiary,
        };
      case 'Profile':
        return {
          icon: focused ? '👤' : '👥',
          gradient: focused,
          bgColor: theme.colors.success,
        };
      default:
        return {
          icon: '❓',
          gradient: false,
          bgColor: theme.colors.outline,
        };
    }
  };

  const iconData = getIconData(routeName);

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      {focused && (
        <View 
          style={[
            styles.background,
            { backgroundColor: `${iconData.bgColor}20` }
          ]} 
        />
      )}
      <Text 
        style={[
          styles.icon, 
          { 
            fontSize: size,
            color: focused ? iconData.bgColor : color,
          }
        ]}
      >
        {iconData.icon}
      </Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 40,
    height: 40,
    position: 'relative',
  },
  background: {
    position: 'absolute',
    width: 36,
    height: 36,
    borderRadius: 18,
    top: 2,
    left: 2,
  },
  icon: {
    textAlign: 'center',
    zIndex: 1,
  },
});

export default TabBarIcon;
