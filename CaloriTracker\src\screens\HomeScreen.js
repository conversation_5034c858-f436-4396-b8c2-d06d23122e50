import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Dimensions,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as Animatable from 'react-native-animatable';
import LinearGradient from 'react-native-linear-gradient';

import {
  MealCard,
  NutritionDisplay,
  DailyProgressChart,
  Button,
  FAB,
  theme,
  commonStyles,
} from '../components';

// Import new modern components
import GradientBackground from '../components/common/GradientBackground';
import AnimatedCard from '../components/common/AnimatedCard';
import FloatingActionButton from '../components/common/FloatingActionButton';
import GlassCard from '../components/common/GlassCard';

import DataService from '../services/dataService';
import { formatDate } from '../utils/formatUtils';
import { debounce, measureScreenLoad } from '../utils/performance';

const { width: screenWidth } = Dimensions.get('window');

/**
 * Home Screen - Shows today's meals and nutrition summary
 */
const HomeScreen = ({ navigation }) => {
  const [todaysMeals, setTodaysMeals] = useState([]);
  const [nutritionSummary, setNutritionSummary] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadTodaysData = useCallback(async () => {
    const endTimer = measureScreenLoad('HomeScreen');

    try {
      setLoading(true);

      // Load today's meals
      const meals = await DataService.getTodaysMeals();
      setTodaysMeals(meals);

      // Load nutrition summary
      const summary = await DataService.getDailyNutritionSummary();
      setNutritionSummary(summary);

    } catch (error) {
      console.error('Error loading today\'s data:', error);
      Alert.alert('Error', 'Failed to load today\'s data. Please try again.');
    } finally {
      setLoading(false);
      endTimer();
    }
  }, []);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadTodaysData();
    setRefreshing(false);
  }, [loadTodaysData]);

  // Load data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      loadTodaysData();
    }, [])
  );

  const handleMealPress = useCallback((meal) => {
    navigation.navigate('MealDetail', { mealId: meal.id });
  }, [navigation]);

  const handleAddMeal = useCallback(() => {
    navigation.navigate('Camera');
  }, [navigation]);

  const handleGoalsPress = useCallback(() => {
    navigation.navigate('Goals');
  }, [navigation]);

  const renderWelcomeMessage = () => {
    const currentHour = new Date().getHours();
    let greeting = 'Good day';
    let greetingIcon = '☀️';

    if (currentHour < 12) {
      greeting = 'Good morning';
      greetingIcon = '🌅';
    } else if (currentHour < 18) {
      greeting = 'Good afternoon';
      greetingIcon = '☀️';
    } else {
      greeting = 'Good evening';
      greetingIcon = '🌙';
    }

    return (
      <Animatable.View
        animation="fadeInDown"
        duration={800}
        style={styles.welcomeContainer}
      >
        <GradientBackground
          variant="primary"
          style={styles.welcomeGradient}
        >
          <View style={styles.welcomeContent}>
            <Text style={styles.greetingIcon}>{greetingIcon}</Text>
            <Text style={styles.welcomeText}>{greeting}!</Text>
            <Text style={styles.dateText}>{formatDate(new Date())}</Text>
          </View>
        </GradientBackground>
      </Animatable.View>
    );
  };

  const renderNutritionSummary = () => {
    if (!nutritionSummary) return null;

    return (
      <Animatable.View
        animation="fadeInUp"
        duration={800}
        delay={400}
        style={styles.summaryContainer}
      >
        <GlassCard
          style={styles.nutritionCard}
          intensity={0.15}
          elevation="lg"
        >
          <View style={styles.summaryHeader}>
            <View style={styles.summaryTitleContainer}>
              <Text style={styles.summaryIcon}>📊</Text>
              <Text style={styles.summaryTitle}>Today's Progress</Text>
            </View>
            <TouchableOpacity
              onPress={handleGoalsPress}
              style={styles.goalsButton}
            >
              <Text style={styles.goalsLink}>Edit Goals</Text>
              <Text style={styles.goalsArrow}>→</Text>
            </TouchableOpacity>
          </View>

          <NutritionDisplay
            nutrition={nutritionSummary.totalNutrition}
            goals={nutritionSummary.goals}
            showPercentages={true}
            showBars={true}
            compact={false}
          />

          <DailyProgressChart
            nutrition={nutritionSummary.totalNutrition}
            goals={nutritionSummary.goals}
          />
        </GlassCard>
      </Animatable.View>
    );
  };

  const quickStatsData = useMemo(() => {
    if (!nutritionSummary) return null;

    const { totalNutrition, goals, remainingCalories } = nutritionSummary;

    return {
      consumed: totalNutrition.calories,
      remaining: remainingCalories,
      meals: todaysMeals.length,
    };
  }, [nutritionSummary, todaysMeals.length]);

  const renderQuickStats = useCallback(() => {
    if (!quickStatsData) return null;

    const statsData = [
      {
        value: quickStatsData.consumed,
        label: 'Consumed',
        icon: '🔥',
        color: theme.colors.calories.primary,
        gradient: theme.colors.calories.gradient,
      },
      {
        value: quickStatsData.remaining,
        label: 'Remaining',
        icon: '⚡',
        color: theme.colors.success,
        gradient: [theme.colors.success, theme.colors.successContainer],
      },
      {
        value: quickStatsData.meals,
        label: 'Meals',
        icon: '🍽️',
        color: theme.colors.info,
        gradient: [theme.colors.info, theme.colors.infoContainer],
      },
    ];

    return (
      <Animatable.View
        animation="slideInUp"
        duration={600}
        delay={200}
        style={styles.quickStatsContainer}
      >
        {statsData.map((stat, index) => (
          <AnimatedCard
            key={stat.label}
            style={styles.statCard}
            animationType="scale"
            onPress={() => {}}
          >
            <LinearGradient
              colors={stat.gradient}
              style={styles.statGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Text style={styles.statIcon}>{stat.icon}</Text>
              <Text style={styles.statValue}>{stat.value}</Text>
              <Text style={styles.statLabel}>{stat.label}</Text>
            </LinearGradient>
          </AnimatedCard>
        ))}
      </Animatable.View>
    );
  }, [quickStatsData]);

  const renderMealsList = () => {
    if (todaysMeals.length === 0) {
      return (
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateIcon}>🍽️</Text>
          <Text style={styles.emptyStateTitle}>No meals logged today</Text>
          <Text style={styles.emptyStateText}>
            Start tracking your nutrition by adding your first meal!
          </Text>
          <Button
            title="Add Your First Meal"
            onPress={handleAddMeal}
            style={styles.emptyStateButton}
          />
        </View>
      );
    }

    // Group meals by type
    const mealsByType = {
      breakfast: todaysMeals.filter(m => m.type === 'breakfast'),
      lunch: todaysMeals.filter(m => m.type === 'lunch'),
      dinner: todaysMeals.filter(m => m.type === 'dinner'),
      snack: todaysMeals.filter(m => m.type === 'snack'),
      other: todaysMeals.filter(m => m.type === 'other'),
    };

    return (
      <View style={styles.mealsContainer}>
        <Text style={styles.mealsTitle}>Today's Meals</Text>
        
        {Object.entries(mealsByType).map(([type, meals]) => {
          if (meals.length === 0) return null;
          
          return (
            <View key={type} style={styles.mealTypeSection}>
              <Text style={styles.mealTypeTitle}>
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </Text>
              {meals.map(meal => (
                <MealCard
                  key={meal.id}
                  meal={meal}
                  onPress={() => handleMealPress(meal)}
                  showImage={true}
                  showNutrition={true}
                  compact={false}
                />
              ))}
            </View>
          );
        })}
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={commonStyles.centerContent}>
        <Text>Loading today's data...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={commonStyles.safeArea}>
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >
        {renderWelcomeMessage()}
        {renderQuickStats()}
        {renderNutritionSummary()}
        {renderMealsList()}
      </ScrollView>
      
      <FloatingActionButton
        onPress={handleAddMeal}
        colors={[theme.colors.primary, theme.colors.primaryContainer]}
        animationType="scale"
        elevation="xxl"
      >
        <Text style={styles.fabIcon}>+</Text>
      </FloatingActionButton>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },

  contentContainer: {
    paddingBottom: 100, // Space for FAB
  },

  // Welcome Section Styles
  welcomeContainer: {
    marginHorizontal: theme.spacing.md,
    marginTop: theme.spacing.sm,
    marginBottom: theme.spacing.lg,
  },

  welcomeGradient: {
    borderRadius: theme.borderRadius.xl,
    overflow: 'hidden',
    ...theme.shadows.lg,
  },

  welcomeContent: {
    padding: theme.spacing.xl,
    alignItems: 'center',
  },

  greetingIcon: {
    fontSize: 48,
    marginBottom: theme.spacing.sm,
  },

  welcomeText: {
    ...theme.typography.headlineMedium,
    color: theme.colors.onPrimary,
    fontWeight: '600',
    marginBottom: theme.spacing.xs,
    textAlign: 'center',
  },

  dateText: {
    ...theme.typography.bodyLarge,
    color: theme.colors.onPrimary,
    opacity: 0.9,
    textAlign: 'center',
  },

  // Quick Stats Styles
  quickStatsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.lg,
    gap: theme.spacing.sm,
  },

  statCard: {
    flex: 1,
    margin: 0,
    padding: 0,
    borderRadius: theme.borderRadius.lg,
    overflow: 'hidden',
  },

  statGradient: {
    padding: theme.spacing.md,
    alignItems: 'center',
    minHeight: 100,
    justifyContent: 'center',
  },

  statIcon: {
    fontSize: 24,
    marginBottom: theme.spacing.xs,
  },

  statValue: {
    ...theme.typography.headlineSmall,
    color: theme.colors.onPrimary,
    fontWeight: '700',
    marginBottom: theme.spacing.xs,
  },

  statLabel: {
    ...theme.typography.labelMedium,
    color: theme.colors.onPrimary,
    opacity: 0.9,
    textAlign: 'center',
  },
  
  // Nutrition Summary Styles
  summaryContainer: {
    marginHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.lg,
  },

  nutritionCard: {
    margin: 0,
    padding: theme.spacing.lg,
  },

  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },

  summaryTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  summaryIcon: {
    fontSize: 24,
    marginRight: theme.spacing.sm,
  },

  summaryTitle: {
    ...theme.typography.titleLarge,
    color: theme.colors.onSurface,
    fontWeight: '600',
  },

  goalsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primaryContainer,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.full,
  },

  goalsLink: {
    ...theme.typography.labelLarge,
    color: theme.colors.onPrimaryContainer,
    fontWeight: '600',
  },

  goalsArrow: {
    ...theme.typography.labelLarge,
    color: theme.colors.onPrimaryContainer,
    marginLeft: theme.spacing.xs,
  },
  
  mealsContainer: {
    marginHorizontal: theme.spacing.md,
  },
  
  mealsTitle: {
    ...theme.typography.h5,
    color: theme.colors.onBackground,
    marginBottom: theme.spacing.md,
  },
  
  mealTypeSection: {
    marginBottom: theme.spacing.lg,
  },
  
  mealTypeTitle: {
    ...theme.typography.h6,
    color: theme.colors.onBackground,
    marginBottom: theme.spacing.sm,
    marginLeft: theme.spacing.sm,
  },
  
  emptyState: {
    ...commonStyles.emptyState,
    marginHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xxl,
  },
  
  emptyStateIcon: {
    fontSize: 64,
    marginBottom: theme.spacing.md,
  },
  
  emptyStateTitle: {
    ...theme.typography.h5,
    color: theme.colors.onBackground,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  
  emptyStateText: {
    ...commonStyles.emptyStateText,
    marginBottom: theme.spacing.lg,
  },
  
  emptyStateButton: {
    alignSelf: 'center',
  },
  
  // FAB Styles
  fabIcon: {
    fontSize: 28,
    color: theme.colors.onPrimary,
    fontWeight: '300',
  },
});

export default HomeScreen;
