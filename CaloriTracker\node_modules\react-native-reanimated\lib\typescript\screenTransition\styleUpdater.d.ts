import type { PanGestureHandlerEventPayload, ScreenTransitionConfig } from './commonTypes';
export declare function applyStyleForBelowTopScreen(screenTransitionConfig: ScreenTransitionConfig, event: PanGestureHandlerEventPayload): void;
export declare function applyStyle(screenTransitionConfig: ScreenTransitionConfig, event: PanGestureHandlerEventPayload): void;
//# sourceMappingURL=styleUpdater.d.ts.map