import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import LinearGradient from 'react-native-linear-gradient';
import { useTheme } from '../../context/ThemeContext';

/**
 * Modern Theme Toggle Component
 */
const ThemeToggle = ({ style, showLabel = true }) => {
  const { theme, isDarkMode, themeMode, setThemeMode } = useTheme();
  const switchValue = useSharedValue(isDarkMode ? 1 : 0);
  const scale = useSharedValue(1);

  React.useEffect(() => {
    switchValue.value = withSpring(isDarkMode ? 1 : 0, theme.animations.spring.gentle);
  }, [isDarkMode]);

  const animatedSwitchStyle = useAnimatedStyle(() => {
    const translateX = interpolate(switchValue.value, [0, 1], [2, 22]);
    
    return {
      transform: [
        { translateX },
        { scale: scale.value },
      ],
    };
  });

  const animatedBackgroundStyle = useAnimatedStyle(() => {
    return {
      backgroundColor: interpolate(
        switchValue.value,
        [0, 1],
        [theme.colors.outline, theme.colors.primary]
      ),
    };
  });

  const handlePress = () => {
    scale.value = withSpring(0.9, theme.animations.spring.snappy);
    scale.value = withSpring(1, theme.animations.spring.bouncy);
    
    if (themeMode === 'system') {
      setThemeMode(isDarkMode ? 'light' : 'dark');
    } else {
      setThemeMode(isDarkMode ? 'light' : 'dark');
    }
  };

  const getThemeIcon = () => {
    if (themeMode === 'system') {
      return '🔄';
    }
    return isDarkMode ? '🌙' : '☀️';
  };

  const getThemeLabel = () => {
    if (themeMode === 'system') {
      return 'Auto';
    }
    return isDarkMode ? 'Dark' : 'Light';
  };

  return (
    <View style={[styles.container, style]}>
      {showLabel && (
        <Text style={[styles.label, { color: theme.colors.onSurface }]}>
          Theme
        </Text>
      )}
      
      <View style={styles.toggleContainer}>
        <TouchableOpacity
          onPress={() => setThemeMode('light')}
          style={[
            styles.modeButton,
            !isDarkMode && themeMode !== 'system' && styles.activeModeButton,
            { borderColor: theme.colors.outline }
          ]}
        >
          <Text style={styles.modeIcon}>☀️</Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => setThemeMode('system')}
          style={[
            styles.modeButton,
            themeMode === 'system' && styles.activeModeButton,
            { borderColor: theme.colors.outline }
          ]}
        >
          <Text style={styles.modeIcon}>🔄</Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => setThemeMode('dark')}
          style={[
            styles.modeButton,
            isDarkMode && themeMode !== 'system' && styles.activeModeButton,
            { borderColor: theme.colors.outline }
          ]}
        >
          <Text style={styles.modeIcon}>🌙</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.switchContainer}>
        <TouchableOpacity onPress={handlePress} style={styles.switch}>
          <Animated.View style={[styles.switchBackground, animatedBackgroundStyle]}>
            <Animated.View style={[styles.switchThumb, animatedSwitchStyle]}>
              <Text style={styles.switchIcon}>{getThemeIcon()}</Text>
            </Animated.View>
          </Animated.View>
        </TouchableOpacity>
        
        {showLabel && (
          <Text style={[styles.themeLabel, { color: theme.colors.onSurface }]}>
            {getThemeLabel()}
          </Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  toggleContainer: {
    flexDirection: 'row',
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 20,
    padding: 4,
    marginBottom: 16,
  },
  modeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 2,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  activeModeButton: {
    backgroundColor: 'rgba(103, 80, 164, 0.1)',
    borderColor: '#6750A4',
  },
  modeIcon: {
    fontSize: 18,
  },
  switchContainer: {
    alignItems: 'center',
  },
  switch: {
    width: 48,
    height: 28,
    borderRadius: 14,
    padding: 2,
  },
  switchBackground: {
    flex: 1,
    borderRadius: 12,
    justifyContent: 'center',
  },
  switchThumb: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 3,
  },
  switchIcon: {
    fontSize: 12,
  },
  themeLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 8,
  },
});

export default ThemeToggle;
