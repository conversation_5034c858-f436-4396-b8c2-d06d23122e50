import React from 'react';
import { View, StyleSheet } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { theme } from '../../theme/theme';

/**
 * Modern Gradient Background Component
 */
const GradientBackground = ({
  children,
  colors,
  start = { x: 0, y: 0 },
  end = { x: 1, y: 1 },
  style,
  variant = 'primary',
  ...props
}) => {
  const getGradientColors = () => {
    if (colors) return colors;
    
    switch (variant) {
      case 'primary':
        return [theme.colors.primary, theme.colors.primaryContainer];
      case 'secondary':
        return [theme.colors.secondary, theme.colors.secondaryContainer];
      case 'calories':
        return theme.colors.calories.gradient;
      case 'protein':
        return theme.colors.protein.gradient;
      case 'carbs':
        return theme.colors.carbs.gradient;
      case 'fat':
        return theme.colors.fat.gradient;
      case 'success':
        return [theme.colors.success, theme.colors.successContainer];
      case 'warning':
        return [theme.colors.warning, theme.colors.warningContainer];
      case 'info':
        return [theme.colors.info, theme.colors.infoContainer];
      case 'sunset':
        return ['#FF6B6B', '#FFE66D', '#FF6B6B'];
      case 'ocean':
        return ['#667eea', '#764ba2'];
      case 'forest':
        return ['#134e5e', '#71b280'];
      case 'purple':
        return ['#667eea', '#764ba2'];
      case 'pink':
        return ['#f093fb', '#f5576c'];
      case 'blue':
        return ['#4facfe', '#00f2fe'];
      default:
        return [theme.colors.primary, theme.colors.primaryContainer];
    }
  };

  return (
    <LinearGradient
      colors={getGradientColors()}
      start={start}
      end={end}
      style={[styles.container, style]}
      {...props}
    >
      {children}
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default GradientBackground;
