import React, { useEffect } from 'react';
import { View, StyleSheet, Pressable } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
} from 'react-native-reanimated';
import { theme, shadows } from '../../theme/theme';

/**
 * Animated Card Component with Modern Interactions
 */
const AnimatedCard = ({
  children,
  style,
  onPress,
  onLongPress,
  disabled = false,
  animationType = 'scale',
  elevation = 'md',
  borderRadius = theme.borderRadius.lg,
  padding = theme.spacing.md,
  margin = theme.spacing.sm,
  backgroundColor = theme.colors.surface,
  pressScale = 0.95,
  springConfig = theme.animations.spring.gentle,
  ...props
}) => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);
  const translateY = useSharedValue(0);
  const rotation = useSharedValue(0);

  useEffect(() => {
    // Entry animation
    scale.value = withSpring(1, springConfig);
    opacity.value = withTiming(1, { duration: theme.animations.duration.medium });
    translateY.value = withSpring(0, springConfig);
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    const scaleValue = animationType === 'scale' ? scale.value : 1;
    const opacityValue = animationType === 'fade' ? opacity.value : 1;
    const translateYValue = animationType === 'slide' ? translateY.value : 0;
    const rotationValue = animationType === 'rotate' ? rotation.value : 0;

    return {
      transform: [
        { scale: scaleValue },
        { translateY: translateYValue },
        { rotate: `${rotationValue}deg` },
      ],
      opacity: opacityValue,
    };
  });

  const handlePressIn = () => {
    if (disabled) return;
    
    switch (animationType) {
      case 'scale':
        scale.value = withSpring(pressScale, springConfig);
        break;
      case 'fade':
        opacity.value = withTiming(0.7, { duration: theme.animations.duration.short });
        break;
      case 'slide':
        translateY.value = withSpring(2, springConfig);
        break;
      case 'rotate':
        rotation.value = withSpring(1, springConfig);
        break;
    }
  };

  const handlePressOut = () => {
    if (disabled) return;
    
    switch (animationType) {
      case 'scale':
        scale.value = withSpring(1, springConfig);
        break;
      case 'fade':
        opacity.value = withTiming(1, { duration: theme.animations.duration.short });
        break;
      case 'slide':
        translateY.value = withSpring(0, springConfig);
        break;
      case 'rotate':
        rotation.value = withSpring(0, springConfig);
        break;
    }
  };

  const cardStyle = {
    backgroundColor,
    borderRadius,
    padding,
    margin,
    ...shadows[elevation],
  };

  if (onPress || onLongPress) {
    return (
      <Pressable
        onPress={onPress}
        onLongPress={onLongPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled}
        style={({ pressed }) => [
          styles.container,
          cardStyle,
          style,
          disabled && styles.disabled,
        ]}
        {...props}
      >
        <Animated.View style={[styles.content, animatedStyle]}>
          {children}
        </Animated.View>
      </Pressable>
    );
  }

  return (
    <Animated.View
      style={[
        styles.container,
        cardStyle,
        animatedStyle,
        style,
        disabled && styles.disabled,
      ]}
      {...props}
    >
      {children}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
  content: {
    flex: 1,
  },
  disabled: {
    opacity: 0.5,
  },
});

export default AnimatedCard;
