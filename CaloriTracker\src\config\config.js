/**
 * Application Configuration
 */

export const API_CONFIG = {
  GEMINI_API_KEY: 'AIzaSyAs0vPoI2hA-oBKiLYYC2qVnKSZSzAR5XE',
  GEMINI_API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent',
  REQUEST_TIMEOUT: 30000, // 30 seconds
};

export const APP_CONFIG = {
  APP_NAME: 'CaloriTracker',
  VERSION: '1.0.0',
  DEFAULT_DAILY_CALORIE_GOAL: 2000,
  DEFAULT_PROTEIN_GOAL: 150, // grams
  DEFAULT_CARB_GOAL: 250, // grams
  DEFAULT_FAT_GOAL: 65, // grams
};

export const STORAGE_KEYS = {
  MEALS: '@CaloriTracker:meals',
  USER_PROFILE: '@CaloriTracker:userProfile',
  DAILY_GOALS: '@CaloriTracker:dailyGoals',
  APP_SETTINGS: '@CaloriTracker:appSettings',
};

export const NUTRITION_GOALS = {
  CALORIES: {
    min: 1200,
    max: 4000,
    default: 2000,
  },
  PROTEIN: {
    min: 50,
    max: 300,
    default: 150,
  },
  CARBS: {
    min: 100,
    max: 500,
    default: 250,
  },
  FAT: {
    min: 30,
    max: 200,
    default: 65,
  },
};

// Modern Color Palette - Material Design 3 & iOS inspired
export const COLORS = {
  // Light Theme Colors
  light: {
    primary: '#6750A4',
    primaryContainer: '#EADDFF',
    onPrimary: '#FFFFFF',
    onPrimaryContainer: '#21005D',

    secondary: '#625B71',
    secondaryContainer: '#E8DEF8',
    onSecondary: '#FFFFFF',
    onSecondaryContainer: '#1D192B',

    tertiary: '#7D5260',
    tertiaryContainer: '#FFD8E4',
    onTertiary: '#FFFFFF',
    onTertiaryContainer: '#31111D',

    background: '#FFFBFE',
    onBackground: '#1C1B1F',
    surface: '#FFFBFE',
    onSurface: '#1C1B1F',
    surfaceVariant: '#E7E0EC',
    onSurfaceVariant: '#49454F',

    outline: '#79747E',
    outlineVariant: '#CAC4D0',
    shadow: '#000000',
    scrim: '#000000',

    error: '#BA1A1A',
    errorContainer: '#FFDAD6',
    onError: '#FFFFFF',
    onErrorContainer: '#410002',

    // Nutrition colors with modern gradients
    calories: {
      primary: '#FF6B6B',
      secondary: '#FF8E8E',
      gradient: ['#FF6B6B', '#FF8E8E'],
    },
    protein: {
      primary: '#4ECDC4',
      secondary: '#7EDDD6',
      gradient: ['#4ECDC4', '#7EDDD6'],
    },
    carbs: {
      primary: '#45B7D1',
      secondary: '#6BC5DB',
      gradient: ['#45B7D1', '#6BC5DB'],
    },
    fat: {
      primary: '#FFA07A',
      secondary: '#FFB599',
      gradient: ['#FFA07A', '#FFB599'],
    },

    // Status colors
    success: '#4CAF50',
    successContainer: '#E8F5E8',
    warning: '#FF9800',
    warningContainer: '#FFF3E0',
    info: '#2196F3',
    infoContainer: '#E3F2FD',
  },

  // Dark Theme Colors
  dark: {
    primary: '#D0BCFF',
    primaryContainer: '#4F378B',
    onPrimary: '#371E73',
    onPrimaryContainer: '#EADDFF',

    secondary: '#CCC2DC',
    secondaryContainer: '#4A4458',
    onSecondary: '#332D41',
    onSecondaryContainer: '#E8DEF8',

    tertiary: '#EFB8C8',
    tertiaryContainer: '#633B48',
    onTertiary: '#492532',
    onTertiaryContainer: '#FFD8E4',

    background: '#1C1B1F',
    onBackground: '#E6E1E5',
    surface: '#1C1B1F',
    onSurface: '#E6E1E5',
    surfaceVariant: '#49454F',
    onSurfaceVariant: '#CAC4D0',

    outline: '#938F99',
    outlineVariant: '#49454F',
    shadow: '#000000',
    scrim: '#000000',

    error: '#FFB4AB',
    errorContainer: '#93000A',
    onError: '#690005',
    onErrorContainer: '#FFDAD6',

    // Nutrition colors for dark theme
    calories: {
      primary: '#FF8A80',
      secondary: '#FFAB91',
      gradient: ['#FF8A80', '#FFAB91'],
    },
    protein: {
      primary: '#80CBC4',
      secondary: '#A7FFEB',
      gradient: ['#80CBC4', '#A7FFEB'],
    },
    carbs: {
      primary: '#81D4FA',
      secondary: '#B3E5FC',
      gradient: ['#81D4FA', '#B3E5FC'],
    },
    fat: {
      primary: '#FFCC02',
      secondary: '#FFD54F',
      gradient: ['#FFCC02', '#FFD54F'],
    },

    // Status colors for dark theme
    success: '#81C784',
    successContainer: '#2E7D32',
    warning: '#FFB74D',
    warningContainer: '#E65100',
    info: '#64B5F6',
    infoContainer: '#1565C0',
  },

  // Chart colors
  chartColors: ['#6750A4', '#625B71', '#7D5260', '#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A'],
};

export const CHART_CONFIG = {
  backgroundColor: '#ffffff',
  backgroundGradientFrom: '#ffffff',
  backgroundGradientTo: '#ffffff',
  decimalPlaces: 0,
  color: (opacity = 1) => `rgba(98, 0, 238, ${opacity})`,
  labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
  style: {
    borderRadius: 16,
  },
  propsForDots: {
    r: '6',
    strokeWidth: '2',
    stroke: '#6200EE',
  },
};

export default {
  API_CONFIG,
  APP_CONFIG,
  STORAGE_KEYS,
  NUTRITION_GOALS,
  COLORS,
  CHART_CONFIG,
};
