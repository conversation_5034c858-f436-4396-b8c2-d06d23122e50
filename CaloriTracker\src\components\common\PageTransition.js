import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  withSequence,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import { theme } from '../../theme/theme';

/**
 * Page Transition Component for Screen Animations
 */
const PageTransition = ({
  children,
  type = 'slideUp',
  duration = theme.animations.duration.medium,
  delay = 0,
  style,
  ...props
}) => {
  const progress = useSharedValue(0);
  const scale = useSharedValue(0.9);
  const opacity = useSharedValue(0);

  useEffect(() => {
    const startAnimation = () => {
      switch (type) {
        case 'slideUp':
          progress.value = withTiming(1, { duration });
          opacity.value = withTiming(1, { duration });
          break;
        case 'slideDown':
          progress.value = withTiming(1, { duration });
          opacity.value = withTiming(1, { duration });
          break;
        case 'slideLeft':
          progress.value = withTiming(1, { duration });
          opacity.value = withTiming(1, { duration });
          break;
        case 'slideRight':
          progress.value = withTiming(1, { duration });
          opacity.value = withTiming(1, { duration });
          break;
        case 'fade':
          opacity.value = withTiming(1, { duration });
          break;
        case 'scale':
          scale.value = withSpring(1, theme.animations.spring.bouncy);
          opacity.value = withTiming(1, { duration });
          break;
        case 'bounce':
          scale.value = withSequence(
            withSpring(1.1, theme.animations.spring.snappy),
            withSpring(1, theme.animations.spring.gentle)
          );
          opacity.value = withTiming(1, { duration });
          break;
        default:
          progress.value = withTiming(1, { duration });
          opacity.value = withTiming(1, { duration });
      }
    };

    if (delay > 0) {
      setTimeout(startAnimation, delay);
    } else {
      startAnimation();
    }
  }, [type, duration, delay]);

  const animatedStyle = useAnimatedStyle(() => {
    let transform = [];

    switch (type) {
      case 'slideUp':
        const translateY = interpolate(
          progress.value,
          [0, 1],
          [50, 0],
          Extrapolate.CLAMP
        );
        transform.push({ translateY });
        break;
      case 'slideDown':
        const translateYDown = interpolate(
          progress.value,
          [0, 1],
          [-50, 0],
          Extrapolate.CLAMP
        );
        transform.push({ translateY: translateYDown });
        break;
      case 'slideLeft':
        const translateX = interpolate(
          progress.value,
          [0, 1],
          [50, 0],
          Extrapolate.CLAMP
        );
        transform.push({ translateX });
        break;
      case 'slideRight':
        const translateXRight = interpolate(
          progress.value,
          [0, 1],
          [-50, 0],
          Extrapolate.CLAMP
        );
        transform.push({ translateX: translateXRight });
        break;
      case 'scale':
      case 'bounce':
        transform.push({ scale: scale.value });
        break;
    }

    return {
      opacity: opacity.value,
      transform,
    };
  });

  return (
    <Animated.View style={[styles.container, animatedStyle, style]} {...props}>
      {children}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default PageTransition;
