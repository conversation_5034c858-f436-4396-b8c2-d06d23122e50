/**
 * Component exports
 */

// Main components
export { default as MealCard } from './MealCard';
export { default as NutritionDisplay } from './NutritionDisplay';
export { 
  default as NutritionChart,
  WeeklyCaloriesChart,
  MacroDistributionChart,
  DailyProgressChart,
  MonthlyTrendChart,
} from './NutritionChart';

// Common components
export { default as Button, FAB, IconButton, ButtonGroup } from './common/Button';
export { default as Input, NumberInput, SearchInput } from './common/Input';

// Modern UI components
export { default as GradientBackground } from './common/GradientBackground';
export { default as AnimatedCard } from './common/AnimatedCard';
export { default as FloatingActionButton } from './common/FloatingActionButton';
export { default as GlassCard } from './common/GlassCard';
export { default as TabBarIcon } from './common/TabBarIcon';
export { default as LoadingSpinner } from './common/LoadingSpinner';
export { default as AnimatedButton } from './common/AnimatedButton';
export { default as PageTransition } from './common/PageTransition';
export { default as ThemeToggle } from './common/ThemeToggle';

// Theme
export { theme, commonStyles } from '../theme/theme';
